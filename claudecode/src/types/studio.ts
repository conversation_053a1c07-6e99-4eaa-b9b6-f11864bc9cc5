export interface HttpRequest {
  id: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH' | 'OPTIONS' | 'HEAD';
  url: string;
  headers: Record<string, string>;
  body?: string;
  timestamp: Date;
}

export interface HttpResponse {
  status: number;
  statusText: string;
  headers: Record<string, string>;
  body: string;
  responseTime: number;
  size: number;
}

export interface RequestHistoryItem {
  id: string;
  request: HttpRequest;
  response?: HttpResponse;
  timestamp: Date;
  name: string;
}

export interface AIMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  type?: 'analysis' | 'payload' | 'finding';
}

export interface PayloadSuggestion {
  id: string;
  title: string;
  description: string;
  payload: string;
  category: 'xss' | 'sql' | 'command' | 'path-traversal' | 'xxe';
  severity: 'low' | 'medium' | 'high' | 'critical';
}

export interface Endpoint {
  id: string;
  url: string;
  method: string;
  status: 'scanned' | 'analyzing' | 'vulnerable' | 'secure';
  lastScan: Date;
  findings: number;
}