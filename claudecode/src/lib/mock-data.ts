import { RequestHistoryItem, PayloadSuggestion, Endpoint } from '@/types/studio';

export const mockEndpoint: Endpoint = {
  id: 'ep-001',
  url: 'https://api.example.com/users/123',
  method: 'GET',
  status: 'scanned',
  lastScan: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
  findings: 3
};

export const mockRequestHistory: RequestHistoryItem[] = [
  {
    id: 'req-001',
    name: 'Basic GET Request',
    timestamp: new Date(Date.now() - 1000 * 60 * 5),
    request: {
      id: 'req-001',
      method: 'GET',
      url: 'https://api.example.com/users/123',
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'PentestTool/1.0',
        'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
      },
      timestamp: new Date(Date.now() - 1000 * 60 * 5)
    },
    response: {
      status: 200,
      statusText: 'OK',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': '156',
        'Cache-Control': 'no-cache'
      },
      body: JSON.stringify({
        id: 123,
        username: 'john_doe',
        email: '<EMAIL>',
        role: 'admin',
        created_at: '2023-01-01T00:00:00Z'
      }, null, 2),
      responseTime: 234,
      size: 156
    }
  },
  {
    id: 'req-002',
    name: 'SQL Injection Test',
    timestamp: new Date(Date.now() - 1000 * 60 * 3),
    request: {
      id: 'req-002',
      method: 'GET',
      url: "https://api.example.com/users/123' OR '1'='1",
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'PentestTool/1.0'
      },
      timestamp: new Date(Date.now() - 1000 * 60 * 3)
    },
    response: {
      status: 500,
      statusText: 'Internal Server Error',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        error: 'Database error: You have an error in your SQL syntax'
      }, null, 2),
      responseTime: 1247,
      size: 78
    }
  },
  {
    id: 'req-003',
    name: 'XSS Payload Test',
    timestamp: new Date(Date.now() - 1000 * 60 * 1),
    request: {
      id: 'req-003',
      method: 'POST',
      url: 'https://api.example.com/comments',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify({
        content: '<script>alert("XSS")</script>',
        user_id: 123
      }, null, 2),
      timestamp: new Date(Date.now() - 1000 * 60 * 1)
    }
  }
];

export const mockPayloadSuggestions: PayloadSuggestion[] = [
  {
    id: 'payload-001',
    title: 'Basic SQL Injection',
    description: 'Test for SQL injection in URL parameters',
    payload: "' OR '1'='1' --",
    category: 'sql',
    severity: 'high'
  },
  {
    id: 'payload-002',
    title: 'XSS Script Tag',
    description: 'Basic script tag injection for XSS testing',
    payload: '<script>alert("XSS")</script>',
    category: 'xss',
    severity: 'medium'
  },
  {
    id: 'payload-003',
    title: 'Command Injection',
    description: 'Test for OS command injection',
    payload: '; cat /etc/passwd',
    category: 'command',
    severity: 'critical'
  },
  {
    id: 'payload-004',
    title: 'Path Traversal',
    description: 'Directory traversal attack',
    payload: '../../../etc/passwd',
    category: 'path-traversal',
    severity: 'high'
  }
];