'use client';

import { useState } from 'react';
import { RequestHistoryItem } from '@/types/studio';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { ClientTime } from '@/components/ui/client-time';
import { 
  Search, 
  Clock, 
  Globe, 
  Filter,
  MoreVertical,
  Trash2,
  Star
} from 'lucide-react';
import { formatResponseTime, getStatusColor } from '@/lib/utils';

interface RequestHistoryProps {
  items: RequestHistoryItem[];
  onSelect: (item: RequestHistoryItem) => void;
  selectedId?: string;
}

export function RequestHistory({ items, onSelect, selectedId }: RequestHistoryProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [filter, setFilter] = useState<'all' | 'success' | 'error'>('all');

  const filteredItems = items.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.request.url.toLowerCase().includes(searchQuery.toLowerCase());
    
    if (!matchesSearch) return false;

    if (filter === 'success') return item.response && item.response.status >= 200 && item.response.status < 400;
    if (filter === 'error') return item.response && item.response.status >= 400;
    
    return true;
  });

  const getMethodColor = (method: string) => {
    const colors = {
      GET: 'bg-blue-500/10 text-blue-600 dark:text-blue-400',
      POST: 'bg-green-500/10 text-green-600 dark:text-green-400',
      PUT: 'bg-yellow-500/10 text-yellow-600 dark:text-yellow-400',
      DELETE: 'bg-red-500/10 text-red-600 dark:text-red-400',
      PATCH: 'bg-purple-500/10 text-purple-600 dark:text-purple-400'
    };
    return colors[method as keyof typeof colors] || colors.GET;
  };

  return (
    <div className="h-full flex flex-col border-r bg-card">
      {/* Header */}
      <div className="p-4 border-b">
        <div className="flex items-center space-x-2 mb-3">
          <h3 className="font-medium">Request History</h3>
          <span className="px-2 py-1 rounded-full text-xs font-semibold bg-secondary text-secondary-foreground">{items.length}</span>
        </div>
        
        {/* Search */}
        <div className="relative mb-3">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search requests..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-9"
          />
        </div>

        {/* Filters */}
        <div className="flex space-x-1">
          <Button
            variant={filter === 'all' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setFilter('all')}
          >
            All
          </Button>
          <Button
            variant={filter === 'success' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setFilter('success')}
          >
            Success
          </Button>
          <Button
            variant={filter === 'error' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setFilter('error')}
          >
            Errors
          </Button>
        </div>
      </div>

      {/* Items List */}
      <div className="flex-1 overflow-auto">
        {filteredItems.length === 0 ? (
          <div className="p-4 text-center text-muted-foreground">
            <Globe className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">No requests found</p>
          </div>
        ) : (
          <div className="p-2 space-y-1">
            {filteredItems.map((item) => (
              <div
                key={item.id}
                onClick={() => onSelect(item)}
                className={`p-3 rounded-lg cursor-pointer transition-colors border ${
                  selectedId === item.id
                    ? 'bg-primary/10 border-primary/20'
                    : 'hover:bg-muted/50 border-transparent'
                }`}
              >
                <div className="flex items-start justify-between mb-2">
                  <h4 className="font-medium text-sm truncate flex-1">{item.name}</h4>
                  <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                    <MoreVertical className="h-3 w-3" />
                  </Button>
                </div>
                
                <div className="flex items-center space-x-2 mb-2">
                  <span className={`px-2 py-1 rounded-full text-xs font-semibold ${getMethodColor(item.request.method)}`}>
                    {item.request.method}
                  </span>
                  {item.response && (
                    <span className={`px-2 py-1 rounded-full text-xs font-semibold border ${getStatusColor(item.response.status)}`}>
                      {item.response.status}
                    </span>
                  )}
                </div>

                <p className="text-xs text-muted-foreground truncate mb-2">
                  {item.request.url}
                </p>

                <div className="flex items-center justify-between text-xs text-muted-foreground">
                  <div className="flex items-center space-x-1">
                    <Clock className="h-3 w-3" />
                    <ClientTime date={item.timestamp} />
                  </div>
                  {item.response && (
                    <span>{formatResponseTime(item.response.responseTime)}</span>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}