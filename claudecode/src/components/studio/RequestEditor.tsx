'use client';

import { useState } from 'react';
import Editor from '@monaco-editor/react';
import { HttpRequest } from '@/types/studio';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select } from '@/components/ui/select';
import { Card } from '@/components/ui/card';
import { Plus, X, Code } from 'lucide-react';
import { formatJson, isValidJson } from '@/lib/utils';

interface RequestEditorProps {
  request: HttpRequest;
  onChange: (request: HttpRequest) => void;
}

export function RequestEditor({ request, onChange }: RequestEditorProps) {
  const [activeTab, setActiveTab] = useState<'headers' | 'body'>('headers');
  const [newHeaderKey, setNewHeaderKey] = useState('');
  const [newHeaderValue, setNewHeaderValue] = useState('');

  const handleUrlChange = (url: string) => {
    onChange({ ...request, url });
  };

  const handleMethodChange = (method: string) => {
    onChange({ ...request, method: method as HttpRequest['method'] });
  };

  const handleAddHeader = () => {
    if (newHeaderKey && newHeaderValue) {
      onChange({
        ...request,
        headers: {
          ...request.headers,
          [newHeaderKey]: newHeaderValue
        }
      });
      setNewHeaderKey('');
      setNewHeaderValue('');
    }
  };

  const handleRemoveHeader = (key: string) => {
    const newHeaders = { ...request.headers };
    delete newHeaders[key];
    onChange({ ...request, headers: newHeaders });
  };

  const handleBodyChange = (body: string | undefined) => {
    onChange({ ...request, body });
  };

  const formatBody = () => {
    if (request.body && isValidJson(request.body)) {
      onChange({ ...request, body: formatJson(request.body) });
    }
  };

  return (
    <div className="h-full flex flex-col">
      {/* URL and Method */}
      <div className="border-b p-4 space-y-3">
        <div className="flex space-x-2">
          <Select 
            value={request.method}
            onChange={(e) => handleMethodChange(e.target.value)}
            className="w-24"
          >
            <option value="GET">GET</option>
            <option value="POST">POST</option>
            <option value="PUT">PUT</option>
            <option value="DELETE">DELETE</option>
            <option value="PATCH">PATCH</option>
            <option value="OPTIONS">OPTIONS</option>
            <option value="HEAD">HEAD</option>
          </Select>
          <Input 
            value={request.url}
            onChange={(e) => handleUrlChange(e.target.value)}
            placeholder="Enter URL..."
            className="flex-1"
          />
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b">
        <div className="flex">
          <button
            onClick={() => setActiveTab('headers')}
            className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
              activeTab === 'headers'
                ? 'border-primary text-primary'
                : 'border-transparent text-muted-foreground hover:text-foreground'
            }`}
          >
            Headers ({Object.keys(request.headers).length})
          </button>
          <button
            onClick={() => setActiveTab('body')}
            className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
              activeTab === 'body'
                ? 'border-primary text-primary'
                : 'border-transparent text-muted-foreground hover:text-foreground'
            }`}
          >
            Body
          </button>
        </div>
      </div>

      {/* Tab Content */}
      <div className="flex-1 overflow-auto">
        {activeTab === 'headers' && (
          <div className="p-4 space-y-4">
            {/* Add Header */}
            <Card className="p-3">
              <div className="flex space-x-2">
                <Input
                  placeholder="Header name"
                  value={newHeaderKey}
                  onChange={(e) => setNewHeaderKey(e.target.value)}
                  className="flex-1"
                />
                <Input
                  placeholder="Header value"
                  value={newHeaderValue}
                  onChange={(e) => setNewHeaderValue(e.target.value)}
                  className="flex-1"
                />
                <Button onClick={handleAddHeader} size="sm">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
            </Card>

            {/* Headers List */}
            <div className="space-y-2">
              {Object.entries(request.headers).map(([key, value]) => (
                <div key={key} className="flex items-center space-x-2 p-2 bg-muted/50 rounded">
                  <code className="text-sm font-mono flex-1">{key}:</code>
                  <code className="text-sm font-mono text-muted-foreground flex-2">{value}</code>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleRemoveHeader(key)}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'body' && (
          <div className="h-full flex flex-col">
            <div className="flex items-center justify-between p-2 border-b">
              <span className="text-sm font-medium">Request Body</span>
              <Button variant="ghost" size="sm" onClick={formatBody}>
                <Code className="h-4 w-4 mr-1" />
                Format JSON
              </Button>
            </div>
            <div className="flex-1">
              <Editor
                height="100%"
                defaultLanguage="json"
                value={request.body || ''}
                onChange={(value) => handleBodyChange(value)}
                theme="vs-dark"
                options={{
                  minimap: { enabled: false },
                  scrollBeyondLastLine: false,
                  fontSize: 13,
                  lineNumbers: 'on',
                  wordWrap: 'on',
                  automaticLayout: true
                }}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
}