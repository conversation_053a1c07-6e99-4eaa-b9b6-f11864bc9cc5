'use client';

import { useState, useEffect, useRef } from 'react';
import { HttpRequest, HttpResponse, AIMessage, PayloadSuggestion } from '@/types/studio';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card } from '@/components/ui/card';
import { ClientTime } from '@/components/ui/client-time';
import { 
  Send, 
  Bot, 
  User, 
  Lightbulb, 
  Target, 
  AlertTriangle,
  Copy,
  ChevronDown,
  Sparkles
} from 'lucide-react';
import { aiService } from '@/lib/ai-service';
import { mockPayloadSuggestions } from '@/lib/mock-data';

interface AIAssistantProps {
  currentRequest?: HttpRequest;
  currentResponse?: HttpResponse;
}

export function AIAssistant({ currentRequest, currentResponse }: AIAssistantProps) {
  const [messages, setMessages] = useState<AIMessage[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [payloadSuggestions] = useState<PayloadSuggestion[]>(mockPayloadSuggestions);
  const [showPayloads, setShowPayloads] = useState(true);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    const userMessage = inputValue.trim();
    setInputValue('');
    setIsLoading(true);

    try {
      const aiResponse = await aiService.sendMessage(userMessage);
      setMessages(aiService.getMessages());
    } catch (error) {
      console.error('Failed to send message:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleQuickAction = (action: string) => {
    setInputValue(action);
  };

  const handleCopyPayload = async (payload: string) => {
    await navigator.clipboard.writeText(payload);
  };

  const getSeverityColor = (severity: string) => {
    const colors = {
      low: 'bg-blue-500/10 text-blue-600 dark:text-blue-400',
      medium: 'bg-yellow-500/10 text-yellow-600 dark:text-yellow-400',
      high: 'bg-orange-500/10 text-orange-600 dark:text-orange-400',
      critical: 'bg-red-500/10 text-red-600 dark:text-red-400'
    };
    return colors[severity as keyof typeof colors] || colors.medium;
  };

  const getCategoryIcon = (category: string) => {
    const icons = {
      sql: '💉',
      xss: '🚨',
      command: '⚡',
      'path-traversal': '📂',
      xxe: '📄'
    };
    return icons[category as keyof typeof icons] || '🔍';
  };

  return (
    <div className="h-full flex flex-col border-l bg-card">
      {/* Header */}
      <div className="p-4 border-b">
        <div className="flex items-center space-x-2">
          <div className="p-2 bg-primary/10 rounded-lg">
            <Bot className="h-5 w-5 text-primary" />
          </div>
          <div>
            <h3 className="font-medium">AI Security Assistant</h3>
            <p className="text-xs text-muted-foreground">Ask me about vulnerabilities</p>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="p-3 border-b">
        <div className="grid grid-cols-1 gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleQuickAction('Analyze this response for vulnerabilities')}
            className="justify-start text-left h-auto p-2"
          >
            <AlertTriangle className="h-4 w-4 mr-2 flex-shrink-0" />
            <span className="text-xs">Analyze for vulnerabilities</span>
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleQuickAction('Generate payloads for this endpoint')}
            className="justify-start text-left h-auto p-2"
          >
            <Target className="h-4 w-4 mr-2 flex-shrink-0" />
            <span className="text-xs">Generate payloads</span>
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleQuickAction('Help me test this endpoint')}
            className="justify-start text-left h-auto p-2"
          >
            <Lightbulb className="h-4 w-4 mr-2 flex-shrink-0" />
            <span className="text-xs">Testing suggestions</span>
          </Button>
        </div>
      </div>

      {/* Payload Suggestions */}
      {showPayloads && (
        <div className="border-b">
          <div className="flex items-center justify-between p-3">
            <h4 className="text-sm font-medium">Suggested Payloads</h4>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowPayloads(!showPayloads)}
            >
              <ChevronDown className="h-4 w-4" />
            </Button>
          </div>
          <div className="p-3 pt-0 space-y-2 max-h-40 overflow-auto">
            {payloadSuggestions.slice(0, 3).map((payload) => (
              <Card key={payload.id} className="p-2">
                <div className="flex items-start justify-between mb-1">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm">{getCategoryIcon(payload.category)}</span>
                    <span className="text-xs font-medium">{payload.title}</span>
                  </div>
                  <span className={`px-2 py-1 rounded-full text-xs font-semibold ${getSeverityColor(payload.severity)}`}>
                    {payload.severity}
                  </span>
                </div>
                <p className="text-xs text-muted-foreground mb-2">{payload.description}</p>
                <div className="flex items-center justify-between">
                  <code className="text-xs bg-muted px-1 py-0.5 rounded flex-1 mr-2 truncate">
                    {payload.payload}
                  </code>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleCopyPayload(payload.payload)}
                  >
                    <Copy className="h-3 w-3" />
                  </Button>
                </div>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Messages */}
      <div className="flex-1 overflow-auto p-3 space-y-3">
        {messages.length === 0 ? (
          <div className="text-center py-8">
            <div className="p-3 bg-primary/10 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
              <Sparkles className="h-6 w-6 text-primary" />
            </div>
            <h4 className="font-medium mb-1">AI Security Assistant</h4>
            <p className="text-sm text-muted-foreground mb-4">
              I'm here to help analyze vulnerabilities and generate payloads
            </p>
            <div className="text-xs text-muted-foreground">
              Try asking: "What vulnerabilities do you see in this response?"
            </div>
          </div>
        ) : (
          messages.map((message) => (
            <div
              key={message.id}
              className={`flex space-x-3 ${
                message.role === 'user' ? 'justify-end' : 'justify-start'
              }`}
            >
              {message.role === 'assistant' && (
                <div className="p-1.5 bg-primary/10 rounded-full flex-shrink-0">
                  <Bot className="h-4 w-4 text-primary" />
                </div>
              )}
              <div
                className={`max-w-[85%] rounded-lg p-3 ${
                  message.role === 'user'
                    ? 'bg-primary text-primary-foreground'
                    : 'bg-muted'
                }`}
              >
                <div className="text-sm whitespace-pre-wrap">{message.content}</div>
                <div className="text-xs opacity-70 mt-1">
                  <ClientTime date={message.timestamp} />
                </div>
              </div>
              {message.role === 'user' && (
                <div className="p-1.5 bg-muted rounded-full flex-shrink-0">
                  <User className="h-4 w-4" />
                </div>
              )}
            </div>
          ))
        )}
        {isLoading && (
          <div className="flex space-x-3">
            <div className="p-1.5 bg-primary/10 rounded-full">
              <Bot className="h-4 w-4 text-primary" />
            </div>
            <div className="bg-muted rounded-lg p-3">
              <div className="flex space-x-1">
                <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce"></div>
                <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
              </div>
            </div>
          </div>
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <div className="border-t p-3">
        <div className="flex space-x-2">
          <Input
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Ask about vulnerabilities..."
            className="flex-1"
          />
          <Button 
            onClick={handleSendMessage}
            disabled={!inputValue.trim() || isLoading}
            size="sm"
          >
            <Send className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}