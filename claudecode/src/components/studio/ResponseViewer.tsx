'use client';

import { useState } from 'react';
import Editor from '@monaco-editor/react';
import { HttpResponse } from '@/types/studio';
import { Button } from '@/components/ui/button';
import { 
  Clock, 
  Database, 
 
  FileText, 
  Download,
  Copy,
  Check
} from 'lucide-react';
import { formatBytes, formatResponseTime, getStatusColor, formatJson, isValidJson } from '@/lib/utils';

interface ResponseViewerProps {
  response?: HttpResponse;
  isLoading?: boolean;
}

export function ResponseViewer({ response, isLoading }: ResponseViewerProps) {
  const [activeTab, setActiveTab] = useState<'body' | 'headers'>('body');
  const [bodyFormat, setBodyFormat] = useState<'raw' | 'formatted'>('formatted');
  const [copied, setCopied] = useState(false);

  const copyToClipboard = async (text: string) => {
    await navigator.clipboard.writeText(text);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const getFormattedBody = () => {
    if (!response?.body) return '';
    
    if (bodyFormat === 'formatted' && isValidJson(response.body)) {
      return formatJson(response.body);
    }
    
    return response.body;
  };

  const getBodyLanguage = () => {
    if (!response?.body) return 'text';
    
    const contentType = response.headers['Content-Type'] || response.headers['content-type'] || '';
    
    if (contentType.includes('application/json')) return 'json';
    if (contentType.includes('text/html')) return 'html';
    if (contentType.includes('text/xml') || contentType.includes('application/xml')) return 'xml';
    if (contentType.includes('text/css')) return 'css';
    if (contentType.includes('application/javascript')) return 'javascript';
    
    return 'text';
  };

  if (isLoading) {
    return (
      <div className="h-full flex items-center justify-center bg-gradient-to-br from-background/50 to-muted/20">
        <div className="text-center space-y-6">
          <div className="relative">
            <div className="animate-spin rounded-full h-12 w-12 border-4 border-primary/20 border-t-primary mx-auto"></div>
            <div className="absolute inset-0 rounded-full h-12 w-12 border-4 border-transparent border-r-primary/40 animate-pulse mx-auto"></div>
          </div>
          <div>
            <p className="text-foreground font-medium">Sending request...</p>
            <p className="text-sm text-muted-foreground">Please wait while we process your request</p>
          </div>
        </div>
      </div>
    );
  }

  if (!response) {
    return (
      <div className="h-full flex items-center justify-center bg-gradient-to-br from-background/50 to-muted/20">
        <div className="text-center space-y-6">
          <div className="rounded-2xl bg-gradient-to-br from-muted/50 to-muted/30 p-6 w-20 h-20 flex items-center justify-center mx-auto shadow-lg">
            <FileText className="h-10 w-10 text-muted-foreground" />
          </div>
          <div>
            <h3 className="font-semibold text-foreground text-lg">No Response Yet</h3>
            <p className="text-sm text-muted-foreground max-w-sm mx-auto">
              Send a request to see the response data, headers, and analysis
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-background/50">
      {/* Response Stats */}
      <div className="border-b bg-muted/20 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-6">
            <div className="flex items-center space-x-3">
              <span className="text-sm font-semibold text-foreground">Status:</span>
              <span className={`px-3 py-1.5 rounded-full text-xs font-semibold status-badge ${getStatusColor(response.status)}`}>
                {response.status} {response.statusText}
              </span>
            </div>
            <div className="flex items-center space-x-2 text-sm text-muted-foreground bg-muted/30 px-3 py-1.5 rounded-lg">
              <Clock className="h-4 w-4" />
              <span className="font-medium">{formatResponseTime(response.responseTime)}</span>
            </div>
            <div className="flex items-center space-x-2 text-sm text-muted-foreground bg-muted/30 px-3 py-1.5 rounded-lg">
              <Database className="h-4 w-4" />
              <span className="font-medium">{formatBytes(response.size)}</span>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => copyToClipboard(response.body)}
              className="hover:bg-accent/50 transition-colors"
            >
              {copied ? <Check className="h-4 w-4 text-green-500" /> : <Copy className="h-4 w-4" />}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="hover:bg-accent/50 transition-colors"
            >
              <Download className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b bg-muted/10">
        <div className="flex">
          <button
            onClick={() => setActiveTab('body')}
            className={`px-6 py-3 text-sm font-medium border-b-2 transition-all duration-200 ${
              activeTab === 'body'
                ? 'border-primary text-primary bg-primary/5'
                : 'border-transparent text-muted-foreground hover:text-foreground hover:bg-accent/30'
            }`}
          >
            Response Body
          </button>
          <button
            onClick={() => setActiveTab('headers')}
            className={`px-6 py-3 text-sm font-medium border-b-2 transition-all duration-200 ${
              activeTab === 'headers'
                ? 'border-primary text-primary bg-primary/5'
                : 'border-transparent text-muted-foreground hover:text-foreground hover:bg-accent/30'
            }`}
          >
            Headers ({Object.keys(response.headers).length})
          </button>
        </div>
      </div>

      {/* Tab Content */}
      <div className="flex-1 overflow-hidden">
        {activeTab === 'body' && (
          <div className="h-full flex flex-col">
            <div className="flex items-center justify-between p-4 border-b bg-muted/20">
              <span className="text-sm font-semibold text-foreground">Body</span>
              <div className="flex items-center space-x-1 bg-muted/50 rounded-lg p-1">
                <button
                  onClick={() => setBodyFormat('raw')}
                  className={`px-3 py-1.5 text-xs rounded-md font-medium transition-colors ${
                    bodyFormat === 'raw'
                      ? 'bg-primary text-primary-foreground shadow-sm'
                      : 'text-muted-foreground hover:text-foreground hover:bg-accent/50'
                  }`}
                >
                  Raw
                </button>
                <button
                  onClick={() => setBodyFormat('formatted')}
                  className={`px-3 py-1.5 text-xs rounded-md font-medium transition-colors ${
                    bodyFormat === 'formatted'
                      ? 'bg-primary text-primary-foreground shadow-sm'
                      : 'text-muted-foreground hover:text-foreground hover:bg-accent/50'
                  }`}
                >
                  Formatted
                </button>
              </div>
            </div>
            <div className="flex-1 bg-background/30">
              <Editor
                height="100%"
                language={getBodyLanguage()}
                value={getFormattedBody()}
                theme="vs-dark"
                options={{
                  readOnly: true,
                  minimap: { enabled: false },
                  scrollBeyondLastLine: false,
                  fontSize: 14,
                  lineNumbers: 'on',
                  wordWrap: 'on',
                  automaticLayout: true,
                  padding: { top: 16, bottom: 16 },
                  fontFamily: 'JetBrains Mono, Consolas, Monaco, monospace',
                  lineHeight: 1.6
                }}
              />
            </div>
          </div>
        )}

        {activeTab === 'headers' && (
          <div className="p-4 space-y-3">
            {Object.entries(response.headers).map(([key, value]) => (
              <div key={key} className="flex items-center p-3 bg-muted/30 rounded-lg border border-border/30 card-hover">
                <code className="text-sm font-mono font-semibold flex-1 text-foreground">{key}:</code>
                <code className="text-sm font-mono text-muted-foreground flex-2 bg-background/50 px-2 py-1 rounded ml-3">{value}</code>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}