'use client';

import { useState } from 'react';
import Editor from '@monaco-editor/react';
import { HttpResponse } from '@/types/studio';
import { Button } from '@/components/ui/button';
import { 
  Clock, 
  Database, 
 
  FileText, 
  Download,
  Copy,
  Check
} from 'lucide-react';
import { formatBytes, formatResponseTime, getStatusColor, formatJson, isValidJson } from '@/lib/utils';

interface ResponseViewerProps {
  response?: HttpResponse;
  isLoading?: boolean;
}

export function ResponseViewer({ response, isLoading }: ResponseViewerProps) {
  const [activeTab, setActiveTab] = useState<'body' | 'headers'>('body');
  const [bodyFormat, setBodyFormat] = useState<'raw' | 'formatted'>('formatted');
  const [copied, setCopied] = useState(false);

  const copyToClipboard = async (text: string) => {
    await navigator.clipboard.writeText(text);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const getFormattedBody = () => {
    if (!response?.body) return '';
    
    if (bodyFormat === 'formatted' && isValidJson(response.body)) {
      return formatJson(response.body);
    }
    
    return response.body;
  };

  const getBodyLanguage = () => {
    if (!response?.body) return 'text';
    
    const contentType = response.headers['Content-Type'] || response.headers['content-type'] || '';
    
    if (contentType.includes('application/json')) return 'json';
    if (contentType.includes('text/html')) return 'html';
    if (contentType.includes('text/xml') || contentType.includes('application/xml')) return 'xml';
    if (contentType.includes('text/css')) return 'css';
    if (contentType.includes('application/javascript')) return 'javascript';
    
    return 'text';
  };

  if (isLoading) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="text-muted-foreground">Sending request...</p>
        </div>
      </div>
    );
  }

  if (!response) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="rounded-full bg-muted p-4 w-16 h-16 flex items-center justify-center mx-auto">
            <FileText className="h-8 w-8 text-muted-foreground" />
          </div>
          <div>
            <h3 className="font-medium">No Response</h3>
            <p className="text-sm text-muted-foreground">Send a request to see the response</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Response Stats */}
      <div className="border-b p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium">Status:</span>
              <span className={`px-2 py-1 rounded-full text-xs font-semibold ${getStatusColor(response.status)}`}>
                {response.status} {response.statusText}
              </span>
            </div>
            <div className="flex items-center space-x-1 text-sm text-muted-foreground">
              <Clock className="h-3 w-3" />
              <span>{formatResponseTime(response.responseTime)}</span>
            </div>
            <div className="flex items-center space-x-1 text-sm text-muted-foreground">
              <Database className="h-3 w-3" />
              <span>{formatBytes(response.size)}</span>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => copyToClipboard(response.body)}
            >
              {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
            </Button>
            <Button variant="ghost" size="sm">
              <Download className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b">
        <div className="flex">
          <button
            onClick={() => setActiveTab('body')}
            className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
              activeTab === 'body'
                ? 'border-primary text-primary'
                : 'border-transparent text-muted-foreground hover:text-foreground'
            }`}
          >
            Response Body
          </button>
          <button
            onClick={() => setActiveTab('headers')}
            className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
              activeTab === 'headers'
                ? 'border-primary text-primary'
                : 'border-transparent text-muted-foreground hover:text-foreground'
            }`}
          >
            Headers ({Object.keys(response.headers).length})
          </button>
        </div>
      </div>

      {/* Tab Content */}
      <div className="flex-1 overflow-hidden">
        {activeTab === 'body' && (
          <div className="h-full flex flex-col">
            <div className="flex items-center justify-between p-2 border-b">
              <span className="text-sm font-medium">Body</span>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setBodyFormat('raw')}
                  className={`px-2 py-1 text-xs rounded ${
                    bodyFormat === 'raw' ? 'bg-primary text-primary-foreground' : 'text-muted-foreground'
                  }`}
                >
                  Raw
                </button>
                <button
                  onClick={() => setBodyFormat('formatted')}
                  className={`px-2 py-1 text-xs rounded ${
                    bodyFormat === 'formatted' ? 'bg-primary text-primary-foreground' : 'text-muted-foreground'
                  }`}
                >
                  Formatted
                </button>
              </div>
            </div>
            <div className="flex-1">
              <Editor
                height="100%"
                language={getBodyLanguage()}
                value={getFormattedBody()}
                theme="vs-dark"
                options={{
                  readOnly: true,
                  minimap: { enabled: false },
                  scrollBeyondLastLine: false,
                  fontSize: 13,
                  lineNumbers: 'on',
                  wordWrap: 'on',
                  automaticLayout: true
                }}
              />
            </div>
          </div>
        )}

        {activeTab === 'headers' && (
          <div className="p-4 space-y-2">
            {Object.entries(response.headers).map(([key, value]) => (
              <div key={key} className="flex p-2 bg-muted/50 rounded">
                <code className="text-sm font-mono font-medium flex-1">{key}:</code>
                <code className="text-sm font-mono text-muted-foreground flex-2">{value}</code>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}