'use client';

import { useState } from 'react';
import { Panel, PanelGroup, PanelResizeHandle } from 'react-resizable-panels';
import { RequestEditor } from './RequestEditor';
import { ResponseViewer } from './ResponseViewer';
import { AIAssistant } from './AIAssistant';
import { RequestHistory } from './RequestHistory';
import { ClientTime } from '@/components/ui/client-time';
import { mockEndpoint, mockRequestHistory } from '@/lib/mock-data';
import { RequestHistoryItem, HttpRequest, HttpResponse } from '@/types/studio';
import { Button } from '@/components/ui/button';
import { 
  Play, 
  History, 
  Settings, 
  Globe, 
  Clock,
  Bug,
  ChevronLeft,
  PanelLeftOpen,
  PanelLeftClose
} from 'lucide-react';

export function StudioLayout() {
  const [currentRequest, setCurrentRequest] = useState<HttpRequest>(mockRequestHistory[0].request);
  const [currentResponse, setCurrentResponse] = useState<HttpResponse | undefined>(mockRequestHistory[0].response);
  const [requestHistory] = useState<RequestHistoryItem[]>(mockRequestHistory);
  const [isHistoryOpen, setIsHistoryOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleSendRequest = async () => {
    setIsLoading(true);
    // Simulate request delay
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
    
    // Mock response based on request
    const mockResponse: HttpResponse = {
      status: currentRequest.url.includes("'") ? 500 : 200,
      statusText: currentRequest.url.includes("'") ? 'Internal Server Error' : 'OK',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': '156',
        'Server': 'nginx/1.18.0'
      },
      body: currentRequest.url.includes("'") 
        ? JSON.stringify({ error: 'Database error: You have an error in your SQL syntax' }, null, 2)
        : JSON.stringify({ id: 123, username: 'john_doe', email: '<EMAIL>' }, null, 2),
      responseTime: Math.floor(Math.random() * 2000) + 100,
      size: 156
    };

    setCurrentResponse(mockResponse);
    setIsLoading(false);
  };

  const handleSelectHistoryItem = (item: RequestHistoryItem) => {
    setCurrentRequest(item.request);
    setCurrentResponse(item.response);
    setIsHistoryOpen(false);
  };

  const getStatusBadge = (status: string) => {
    const colors = {
      scanned: 'bg-blue-500/10 text-blue-600 dark:text-blue-400',
      analyzing: 'bg-yellow-500/10 text-yellow-600 dark:text-yellow-400',
      vulnerable: 'bg-red-500/10 text-red-600 dark:text-red-400',
      secure: 'bg-green-500/10 text-green-600 dark:text-green-400'
    };
    return colors[status as keyof typeof colors] || colors.scanned;
  };

  return (
    <div className="h-screen flex flex-col bg-background">
      {/* Header */}
      <div className="border-b bg-card px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="icon" onClick={() => window.history.back()}>
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <div className="flex items-center space-x-3">
              <Globe className="h-5 w-5 text-muted-foreground" />
              <div>
                <h1 className="text-lg font-semibold">{mockEndpoint.url}</h1>
                <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                  <span className="font-medium">{mockEndpoint.method}</span>
                  <span>•</span>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusBadge(mockEndpoint.status)}`}>
                    {mockEndpoint.status}
                  </span>
                  <span>•</span>
                  <div className="flex items-center space-x-1">
                    <Clock className="h-3 w-3" />
                    <span>Last scan: </span>
                    <ClientTime date={mockEndpoint.lastScan} />
                  </div>
                  <span>•</span>
                  <div className="flex items-center space-x-1">
                    <Bug className="h-3 w-3" />
                    <span>{mockEndpoint.findings} findings</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => setIsHistoryOpen(!isHistoryOpen)}
            >
              {isHistoryOpen ? <PanelLeftClose className="h-4 w-4" /> : <PanelLeftOpen className="h-4 w-4" />}
              History
            </Button>
            <Button variant="outline" size="sm">
              <Settings className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-hidden">
        <PanelGroup direction="horizontal">
          {/* History Sidebar */}
          {isHistoryOpen && (
            <>
              <Panel defaultSize={20} minSize={15} maxSize={30}>
                <RequestHistory 
                  items={requestHistory}
                  onSelect={handleSelectHistoryItem}
                  selectedId={currentRequest.id}
                />
              </Panel>
              <PanelResizeHandle className="w-1 bg-border hover:bg-accent transition-colors" />
            </>
          )}

          {/* Main Workspace */}
          <Panel defaultSize={isHistoryOpen ? 56 : 70} minSize={40}>
            <PanelGroup direction="vertical">
              {/* Request Editor */}
              <Panel defaultSize={50} minSize={30}>
                <div className="h-full flex flex-col">
                  <div className="flex items-center justify-between border-b px-4 py-2 bg-muted/50">
                    <h3 className="font-medium">Request</h3>
                    <Button 
                      onClick={handleSendRequest}
                      disabled={isLoading}
                      size="sm"
                    >
                      <Play className="h-4 w-4 mr-1" />
                      {isLoading ? 'Sending...' : 'Send'}
                    </Button>
                  </div>
                  <div className="flex-1">
                    <RequestEditor 
                      request={currentRequest}
                      onChange={setCurrentRequest}
                    />
                  </div>
                </div>
              </Panel>

              <PanelResizeHandle className="h-1 bg-border hover:bg-accent transition-colors" />

              {/* Response Viewer */}
              <Panel defaultSize={50} minSize={30}>
                <div className="h-full flex flex-col">
                  <div className="border-b px-4 py-2 bg-muted/50">
                    <h3 className="font-medium">Response</h3>
                  </div>
                  <div className="flex-1">
                    <ResponseViewer 
                      response={currentResponse}
                      isLoading={isLoading}
                    />
                  </div>
                </div>
              </Panel>
            </PanelGroup>
          </Panel>

          <PanelResizeHandle className="w-1 bg-border hover:bg-accent transition-colors" />

          {/* AI Assistant */}
          <Panel defaultSize={isHistoryOpen ? 24 : 30} minSize={20} maxSize={40}>
            <AIAssistant 
              currentRequest={currentRequest}
              currentResponse={currentResponse}
            />
          </Panel>
        </PanelGroup>
      </div>
    </div>
  );
}