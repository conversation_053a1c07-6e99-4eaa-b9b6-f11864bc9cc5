'use client';

import { useState, useEffect } from 'react';

interface ClientTimeProps {
  date: Date;
  className?: string;
}

export function ClientTime({ date, className }: ClientTimeProps) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return <span className={className}>--:--:--</span>;
  }

  return <span className={className}>{date.toLocaleTimeString()}</span>;
}